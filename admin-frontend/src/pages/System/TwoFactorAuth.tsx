import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useAuth } from '@/context/AuthContext';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Modal from '@/components/common/Modal';
import Card from '@/components/common/Card';
import { useToast } from '@/hooks/useToast';
import { useApi } from '@/hooks/useApi';

interface TwoFactorSetup {
  secret: string;
  qrCode: string;
  manualEntryKey: string;
}

interface BackupCodesResponse {
  backupCodes: string[];
}

interface VerifyForm {
  token: string;
}

interface DisableForm {
  password: string;
  token?: string;
}

const TwoFactorAuth: React.FC = () => {
  const { user } = useAuth();
  const { showToast } = useToast();
  const [setupData, setSetupData] = useState<TwoFactorSetup | null>(null);
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [showSetupModal, setShowSetupModal] = useState(false);
  const [showBackupCodesModal, setShowBackupCodesModal] = useState(false);
  const [showDisableModal, setShowDisableModal] = useState(false);
  const [isEnabled, setIsEnabled] = useState(false);

  const {
    register: registerVerify,
    handleSubmit: handleSubmitVerify,
    formState: { errors: verifyErrors },
    reset: resetVerify
  } = useForm<VerifyForm>();

  const {
    register: registerDisable,
    handleSubmit: handleSubmitDisable,
    formState: { errors: disableErrors },
    reset: resetDisable
  } = useForm<DisableForm>();

  // API 调用
  const {
    loading: setupLoading,
    execute: executeSetup
  } = useApi(async () => {
    const response = await fetch('/api/admin/auth/2fa/setup', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) throw new Error('Setup failed');
    return response.json();
  });

  const {
    loading: verifyLoading,
    execute: executeVerify
  } = useApi(async (data: VerifyForm) => {
    const response = await fetch('/api/admin/auth/2fa/verify', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('Verification failed');
    return response.json();
  });

  const {
    loading: disableLoading,
    execute: executeDisable
  } = useApi(async (data: DisableForm) => {
    const response = await fetch('/api/admin/auth/2fa/disable', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('Disable failed');
    return response.json();
  });

  const {
    loading: backupCodesLoading,
    execute: executeGetBackupCodes
  } = useApi(async () => {
    const response = await fetch('/api/admin/auth/2fa/backup-codes', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) throw new Error('Failed to get backup codes');
    return response.json();
  });

  // 检查当前用户是否已启用 2FA
  useEffect(() => {
    // 这里应该从用户信息中获取 2FA 状态
    // 暂时假设从 localStorage 或其他地方获取
    const checkTwoFactorStatus = async () => {
      try {
        const response = await fetch('/api/admin/auth/me', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
          }
        });
        if (response.ok) {
          const userData = await response.json();
          setIsEnabled(userData.data?.twoFactorEnabled || false);
        }
      } catch (error) {
        console.error('Failed to check 2FA status:', error);
      }
    };

    checkTwoFactorStatus();
  }, []);

  // 开始设置 2FA
  const handleSetup = async () => {
    try {
      const result = await executeSetup();
      if (result.success) {
        setSetupData(result.data);
        setShowSetupModal(true);
      } else {
        showToast(result.message || '设置失败', 'error');
      }
    } catch (error) {
      showToast('设置失败', 'error');
    }
  };

  // 验证并启用 2FA
  const handleVerify = async (data: VerifyForm) => {
    try {
      const result = await executeVerify(data);
      if (result.success) {
        setBackupCodes(result.data.backupCodes);
        setShowSetupModal(false);
        setShowBackupCodesModal(true);
        setIsEnabled(true);
        resetVerify();
        showToast('两步验证已启用', 'success');
      } else {
        showToast(result.message || '验证失败', 'error');
      }
    } catch (error) {
      showToast('验证失败', 'error');
    }
  };

  // 禁用 2FA
  const handleDisable = async (data: DisableForm) => {
    try {
      const result = await executeDisable(data);
      if (result.success) {
        setIsEnabled(false);
        setShowDisableModal(false);
        resetDisable();
        showToast('两步验证已禁用', 'success');
      } else {
        showToast(result.message || '禁用失败', 'error');
      }
    } catch (error) {
      showToast('禁用失败', 'error');
    }
  };

  // 获取新的备用码
  const handleGetBackupCodes = async () => {
    try {
      const result = await executeGetBackupCodes();
      if (result.success) {
        setBackupCodes(result.data.backupCodes);
        setShowBackupCodesModal(true);
      } else {
        showToast(result.message || '获取备用码失败', 'error');
      }
    } catch (error) {
      showToast('获取备用码失败', 'error');
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">两步验证</h1>
        <p className="mt-1 text-sm text-gray-600">
          为您的管理员账户添加额外的安全保护
        </p>
      </div>

      {/* 2FA 状态卡片 */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                两步验证状态
              </h3>
              <p className="mt-1 text-sm text-gray-600">
                {isEnabled 
                  ? '您的账户已启用两步验证保护' 
                  : '建议启用两步验证以提高账户安全性'
                }
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                isEnabled 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {isEnabled ? '已启用' : '未启用'}
              </span>
            </div>
          </div>

          <div className="mt-6 flex space-x-3">
            {!isEnabled ? (
              <Button
                onClick={handleSetup}
                loading={setupLoading}
                variant="primary"
              >
                启用两步验证
              </Button>
            ) : (
              <>
                <Button
                  onClick={handleGetBackupCodes}
                  loading={backupCodesLoading}
                  variant="secondary"
                >
                  查看备用码
                </Button>
                <Button
                  onClick={() => setShowDisableModal(true)}
                  variant="danger"
                >
                  禁用两步验证
                </Button>
              </>
            )}
          </div>
        </div>
      </Card>

      {/* 设置 2FA 模态框 */}
      {showSetupModal && setupData && (
        <Modal
          isOpen={showSetupModal}
          onClose={() => setShowSetupModal(false)}
          title="设置两步验证"
          size="lg"
        >
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                扫描二维码
              </h3>
              <div className="flex justify-center mb-4">
                <img 
                  src={setupData.qrCode} 
                  alt="2FA QR Code" 
                  className="border rounded-lg"
                />
              </div>
              <p className="text-sm text-gray-600 mb-4">
                使用 Google Authenticator 或其他 TOTP 应用扫描上方二维码
              </p>
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-xs text-gray-500 mb-1">手动输入密钥：</p>
                <code className="text-sm font-mono break-all">
                  {setupData.manualEntryKey}
                </code>
              </div>
            </div>

            <form onSubmit={handleSubmitVerify(handleVerify)} className="space-y-4">
              <Input
                label="验证码"
                placeholder="请输入6位验证码"
                {...registerVerify('token', {
                  required: '请输入验证码',
                  pattern: {
                    value: /^\d{6}$/,
                    message: '验证码必须是6位数字'
                  }
                })}
                error={verifyErrors.token?.message}
                maxLength={6}
              />

              <div className="flex justify-end space-x-3">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => setShowSetupModal(false)}
                >
                  取消
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  loading={verifyLoading}
                >
                  验证并启用
                </Button>
              </div>
            </form>
          </div>
        </Modal>
      )}

      {/* 备用码模态框 */}
      {showBackupCodesModal && (
        <Modal
          isOpen={showBackupCodesModal}
          onClose={() => setShowBackupCodesModal(false)}
          title="备用恢复码"
          size="md"
        >
          <div className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    重要提醒
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>请将这些备用码保存在安全的地方。每个备用码只能使用一次。</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-2">
              {backupCodes.map((code, index) => (
                <div key={index} className="bg-gray-50 p-2 rounded text-center font-mono text-sm">
                  {code}
                </div>
              ))}
            </div>

            <div className="flex justify-end">
              <Button
                onClick={() => setShowBackupCodesModal(false)}
                variant="primary"
              >
                我已保存
              </Button>
            </div>
          </div>
        </Modal>
      )}

      {/* 禁用 2FA 模态框 */}
      {showDisableModal && (
        <Modal
          isOpen={showDisableModal}
          onClose={() => setShowDisableModal(false)}
          title="禁用两步验证"
          size="md"
        >
          <form onSubmit={handleSubmitDisable(handleDisable)} className="space-y-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    安全警告
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>禁用两步验证将降低您账户的安全性。请确认您要继续此操作。</p>
                  </div>
                </div>
              </div>
            </div>

            <Input
              label="当前密码"
              type="password"
              placeholder="请输入当前密码"
              {...registerDisable('password', {
                required: '请输入当前密码'
              })}
              error={disableErrors.password?.message}
            />

            <Input
              label="验证码（可选）"
              placeholder="请输入6位验证码或备用码"
              {...registerDisable('token')}
              error={disableErrors.token?.message}
            />

            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                variant="secondary"
                onClick={() => setShowDisableModal(false)}
              >
                取消
              </Button>
              <Button
                type="submit"
                variant="danger"
                loading={disableLoading}
              >
                确认禁用
              </Button>
            </div>
          </form>
        </Modal>
      )}
    </div>
  );
};

export default TwoFactorAuth;
